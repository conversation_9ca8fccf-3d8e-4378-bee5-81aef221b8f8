## 7. 后台管理系统使用指南

### 后台访问方式

#### 管理员登录页面
**访问URL**: `http://your-domain.com/users/login`
- 开发环境: `http://localhost:8765/users/login`
- 生产环境: `http://xinlingyu.local/users/login`

#### 默认管理员账户
**用户名**: `admin`
**密码**: `xly2021`
**邮箱**: `<EMAIL>`

> ⚠️ **安全提醒**: 首次登录后请立即修改默认密码

#### 登录流程
1. **访问登录页面**: 在浏览器中输入登录URL
2. **输入凭据**:
   - 用户名字段输入: `admin`
   - 密码字段输入: `xly2021`
3. **点击登录**: 点击"登录"按钮
4. **验证成功**: 系统验证通过后自动跳转到首页
5. **权限确认**: 登录后可访问所有后台管理功能

#### 权限验证机制
**认证系统**: 基于CakePHP Auth组件
- **会话管理**: 使用PHP Session存储登录状态
- **权限控制**: 通过`isAuthorized()`方法控制访问权限
- **自动登出**: 长时间无操作自动退出登录
- **CSRF保护**: 表单提交包含CSRF令牌验证

### 后台功能模块详解

#### 1. 用户管理模块

**访问路径**: `/users`

**功能概述**: 管理系统用户账户，包括管理员和一般用户的创建、编辑、查看和删除。

**用户角色说明**:
- **管理员用户** (role=0): 拥有所有系统权限
- **一般用户** (role=1): 受限权限，主要用于内容创建

**操作步骤**:

**查看用户列表**:
1. 登录后台系统
2. 访问 `/users` 或点击用户管理菜单
3. 页面显示所有用户的基本信息：
   - 用户名、姓名、系统权限、创建日期
   - 操作选项：查看、编辑、删除

**添加新用户**:
1. 在用户列表页面点击"新建用户"按钮
2. 填写必填信息：
   - **用户名**: 系统登录用户名（必填）
   - **姓名**: 用户真实姓名（必填）
   - **性别**: 选择男/女
   - **联系电话**: 用户联系方式
   - **邮箱**: 电子邮箱地址（必填）
   - **住址**: 用户地址信息
   - **系统权限**: 选择管理员或一般用户
3. 点击"确定"按钮保存
4. 系统自动设置默认密码: `xly2021`

**编辑用户信息**:
1. 在用户列表中点击对应用户的"编辑"按钮
2. 修改需要更新的字段
3. 点击"更新"按钮保存更改
4. 系统显示更新成功提示

**删除用户**:
1. 在用户列表中点击"删除"按钮
2. 确认删除操作
3. 系统执行删除并显示结果

#### 2. 文章管理模块

**访问路径**: `/articles`

**功能概述**: 管理教育文章内容，支持富文本编辑、图片上传、分类管理等功能。

**操作步骤**:

**查看文章列表**:
1. 访问 `/articles`
2. 页面显示文章列表，包含：
   - 文章标题、分类、作者、创建时间
   - 搜索功能：按标题关键词和分类筛选
   - 分页显示，支持大量文章管理

**发布新文章**:
1. 点击"新建文章"按钮
2. 填写文章信息：
   - **文章分类**: 从下拉列表选择或新建分类
   - **文章封面**: 上传横幅图片（支持jpg, png, gif格式）
   - **文章标题**: 输入文章标题（必填）
   - **文章内容**: 使用CKEditor富文本编辑器编写内容
3. 内容编辑功能：
   - 文本格式化（粗体、斜体、下划线等）
   - 插入图片、链接、表格
   - 代码块、引用块
   - 多级标题设置
4. 点击"确定"发布文章

**编辑文章**:
1. 在文章列表中点击"编辑"按钮
2. 修改文章内容和属性
3. 更新封面图片（可选）
4. 保存更改

**文章分类管理**:
- 在添加/编辑文章时可以创建新分类
- 分类支持中日双语名称
- 分类按学部(0)和大学院(1)区分

#### 3. 课程管理模块

**访问路径**: `/lessons`

**功能概述**: 管理课程信息，支持中日双语课程描述、课程资料上传等。

**操作步骤**:

**添加新课程**:
1. 访问 `/lessons/add`
2. 填写课程基本信息：
   - **课程分类**: 选择或创建课程分类
   - **中文标题**: 课程的中文名称
   - **日文标题**: 课程的日文名称
   - **中文描述**: 详细的中文课程介绍
   - **日文描述**: 详细的日文课程介绍
   - **课程资料**: 上传相关文档或资料文件
3. 保存课程信息

**课程信息维护**:
1. 在课程列表中选择要编辑的课程
2. 更新课程内容和资料
3. 支持多语言内容同步更新

#### 4. 师资管理模块

**访问路径**: `/teachers`

**功能概述**: 管理教师信息，包括教师简介、头像、专业领域等。

**操作步骤**:

**录入教师信息**:
1. 访问 `/teachers/add`
2. 填写教师基本信息：
   - **师资分类**: 选择教师所属分类
   - **中文姓名**: 教师中文姓名
   - **日文姓名**: 教师日文姓名
   - **教师头像**: 上传教师照片
   - **中文简介**: 教师的中文介绍
   - **日文简介**: 教师的日文介绍
   - **排序权重**: 设置教师在列表中的显示顺序
3. 保存教师信息

**教师信息管理**:
- 支持教师信息的查看、编辑、删除
- 头像图片自动处理和优化
- 排序功能便于前台展示管理

#### 5. 视频管理模块

**访问路径**: `/movies`

**功能概述**: 管理试听视频内容，支持视频缩略图、多语言描述等。

**操作步骤**:

**上传新视频**:
1. 访问 `/movies/add`
2. 填写视频信息：
   - **视频分类**: 选择视频所属分类
   - **视频缩略图**: 上传视频预览图片
   - **中文标题**: 视频的中文标题
   - **日文标题**: 视频的日文标题
   - **中文描述**: 视频的中文说明
   - **日文描述**: 视频的日文说明
   - **视频链接**: 输入视频文件URL或嵌入代码
3. 保存视频信息

**视频内容编辑**:
- 支持视频信息的完整编辑
- 缩略图可以重新上传替换
- 视频链接支持多种格式

#### 6. 分类管理模块

**访问路径**: `/items`

**功能概述**: 统一管理所有内容的分类，包括课程、文章、视频、师资的分类设置。

**分类类型说明**:
- **block=0**: 课程分类
- **block=1**: 文章分类
- **block=2**: 视频分类
- **block=3**: 师资分类

**部门类型说明**:
- **dept=0**: 学部（本科）
- **dept=1**: 大学院（研究生）

**操作步骤**:

**创建新分类**:
1. 访问 `/items/add`
2. 设置分类参数：
   - **分类类型**: 选择内容类型（课程/文章/视频/师资）
   - **所属部门**: 选择学部或大学院
   - **中文名称**: 分类的中文名称
   - **日文名称**: 分类的日文名称
   - **中文描述**: 分类的中文说明
   - **日文描述**: 分类的日文说明
   - **分类图标**: 上传分类图标（可选）
3. 保存分类设置

**分类维护**:
- 支持分类信息的编辑和删除
- 删除分类前需确保没有关联内容
- 分类支持多语言显示

#### 7. 图片管理模块

**访问路径**: `/imgs/add`

**功能概述**: 管理按年代组织的图片库，主要用于学习成果展示。

**操作步骤**:

**上传图片**:
1. 访问图片上传页面
2. 选择图片文件：
   - **支持格式**: JPG, PNG, GIF
   - **文件大小**: 建议不超过5MB
   - **图片尺寸**: 建议宽度不超过1920px
3. 选择所属年代和部门
4. 批量上传多张图片

**图片组织管理**:
- 图片按年代（decades）分组
- 支持学部和大学院分别管理
- 自动生成缩略图
- 前台按时间倒序显示

### 操作流程说明

#### 多语言内容录入方法

**中日双语内容管理**:
1. **数据库层面**: 所有支持多语言的字段都有对应的中文和日文版本
   - 标题字段: `title_ch` (中文) / `title_jp` (日文)
   - 描述字段: `description_ch` (中文) / `description_jp` (日文)
   - 姓名字段: `name_ch` (中文) / `name_jp` (日文)

2. **录入流程**:
   - 在添加/编辑内容时，同时填写中文和日文版本
   - 中文内容为必填项，日文内容建议填写
   - 系统会根据用户语言偏好显示对应版本

3. **语言切换验证**:
   - 录入完成后，使用前台语言切换功能验证显示效果
   - 确保中日文内容都能正确显示
   - 检查格式和排版是否正常

#### 文件上传注意事项

**图片文件上传**:
- **支持格式**: JPG, JPEG, PNG, GIF
- **文件大小限制**: 单个文件不超过5MB
- **推荐尺寸**:
  - 文章封面: 1200x600px
  - 教师头像: 400x400px
  - 视频缩略图: 1280x720px
- **命名规范**: 使用英文和数字，避免中文文件名

**文档文件上传**:
- **支持格式**: PDF, DOC, DOCX, PPT, PPTX
- **文件大小限制**: 单个文件不超过10MB
- **存储位置**: 自动存储到 `webroot/files/` 对应目录

**上传安全注意事项**:
- 系统会自动检查文件类型和大小
- 恶意文件会被自动拒绝
- 上传的文件会重命名以避免冲突

#### 数据备份和恢复建议

**定期备份操作**:
1. **数据库备份**:
```bash
# 手动备份数据库
mysqldump -u username -p dbforxinlingyu > backup_$(date +%Y%m%d).sql

# 设置自动备份（添加到crontab）
0 2 * * * mysqldump -u username -p'password' dbforxinlingyu > /backup/db_$(date +\%Y\%m\%d).sql
```

2. **文件备份**:
```bash
# 备份上传文件目录
tar -czf files_backup_$(date +%Y%m%d).tar.gz webroot/files/

# 备份整个项目
tar -czf project_backup_$(date +%Y%m%d).tar.gz --exclude=vendor --exclude=tmp .
```

3. **备份验证**:
   - 定期测试备份文件的完整性
   - 在测试环境验证恢复流程
   - 保留至少30天的备份历史

**数据恢复流程**:
1. **数据库恢复**:
```bash
# 恢复数据库
mysql -u username -p dbforxinlingyu < backup_20240101.sql
```

2. **文件恢复**:
```bash
# 恢复文件目录
tar -xzf files_backup_20240101.tar.gz
```

3. **验证恢复结果**:
   - 检查网站功能是否正常
   - 验证图片和文件是否可以正常访问
   - 测试用户登录和权限功能

### 权限和安全

#### 管理员权限级别说明

**权限分级**:
- **超级管理员** (role=0):
  - 拥有所有系统功能权限
  - 可以管理用户账户
  - 可以修改系统配置
  - 可以访问所有内容管理功能

- **一般用户** (role=1):
  - 仅能创建和编辑自己的内容
  - 无法管理其他用户
  - 无法修改系统设置
  - 受限的功能访问权限

**权限控制机制**:
- 基于CakePHP Auth组件的角色验证
- 每个控制器方法都有权限检查
- 前端界面根据权限动态显示功能

#### 安全操作建议

**账户安全**:
1. **密码策略**:
   - 使用强密码（至少8位，包含字母、数字、特殊字符）
   - 定期更换密码（建议3个月一次）
   - 不要使用默认密码 `xly2021`

2. **登录安全**:
   - 避免在公共网络环境下登录
   - 使用完毕后及时退出登录
   - 不要保存密码在浏览器中

3. **操作安全**:
   - 重要操作前先备份数据
   - 删除操作需要二次确认
   - 定期检查用户账户和权限设置

#### 密码修改和账户安全设置

**修改管理员密码**:
1. 登录后台系统
2. 访问用户管理页面 `/users`
3. 找到admin用户，点击"编辑"
4. 在编辑页面修改密码字段
5. 保存更改

> 注意：当前系统密码是明文存储，建议升级到加密存储

**账户安全检查清单**:
- [ ] 已修改默认管理员密码
- [ ] 已删除不必要的用户账户
- [ ] 已设置合适的用户权限级别
- [ ] 已配置定期数据备份
- [ ] 已检查文件上传权限设置
- [ ] 已验证CSRF保护功能

**安全监控建议**:
- 定期检查系统日志文件
- 监控异常登录活动
- 及时更新系统和依赖包
- 配置Web服务器安全头
- 启用HTTPS加密传输


-- 登录MySQL数据库
mysql -u username -p dbforxinlingyu

-- 修改admin用户密码
UPDATE users SET password = 'your_new_secure_password' WHERE username = 'admin';